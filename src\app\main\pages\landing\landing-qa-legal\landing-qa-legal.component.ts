import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';
import { CoreConfigService } from '@core/services/config.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LandingQAService, QAField, QA, QAListResponse } from './landing-qa-legal.service';

interface QAItem {
    id: string;
    title: string;
    cat: string;
    date: string;
    question: string;
    answer: string;
}

interface Category {
    id: string;
    name: string;
    count: number;
    description: string;
}

interface PaginationPage {
    label: string;
    value: number | string;
    disabled?: boolean;
}

@Component({
    selector: 'app-landing-qa-legal',
    templateUrl: './landing-qa-legal.component.html',
    styleUrls: ['./landing-qa-legal.component.scss']
})
export class LandingQaLegalComponent implements OnInit, OnD<PERSON>roy {
    // Data
    public activeSection: 'chatbot' | 'information' | 'mission' | 'articles' | 'qa-legal' | 'contact' = 'information';
    private _unsubscribeAll: Subject<any>;
    public showBackToTop = false;
    public isScrolled = false;
    public urls = {
        url1: "https://drive.google.com/drive/folders/1kMQUNE7zD7K277-F6Kh7dnl6rh8NqpWC?usp=drive_link", // Replace with your first URL
        url2: "https://forms.gle/xB7ao9UjBKNPFt8h7", // Replace with your second URL
    };

    categories: Category[] = []
    constructor(
        private _coreConfigService: CoreConfigService,
        private router: Router,
        private route: ActivatedRoute,
        private landingQAService: LandingQAService
    ) {
        this._unsubscribeAll = new Subject();

        // Configure the layout
        this._coreConfigService.config = {
            layout: {
                navbar: {
                    hidden: true,
                },
                menu: {
                    hidden: true,
                },
                footer: {
                    hidden: true,
                },
                customizer: false,
                enableLocalStorage: false,
            },
        };
    }
    // State
    currentCategory = '';
    searchQuery = '';
    currentPage = 1;
    pageSize = 15;
    showModal = false;
    selectedItem: QAItem | null = null;

    // API Data
    qaFields: QAField[] = [];
    qaList: QA[] = [];
    totalQAs = 0;
    isLoading = false;
    selectedQADetail: QA | null = null;

    // Computed properties
    get filteredData(): QA[] {
        // Dữ liệu đã được filter từ API, trả về trực tiếp
        return this.qaList;
    }

    get paginatedData(): QA[] {
        // Dữ liệu đã được phân trang từ API, trả về trực tiếp
        return this.qaList;
    }

    get pageTitle(): string {
        const total = this.totalQAs;
        if (this.currentCategory === '') {
            return `Tất cả câu hỏi (${total})`;
        }
        const categoryName = this.categories.find(cat => cat.id === this.currentCategory)?.name || this.currentCategory;
        return `${categoryName} (${total})`;
    }

    get totalPages(): number {
        return Math.ceil(this.totalQAs / this.pageSize) || 1;
    }
    @HostListener('window:scroll')
    onWindowScroll(): void {
        this.isScrolled = window.scrollY > 4;

        this.showBackToTop =
            (window.pageYOffset ||
                document.documentElement.scrollTop ||
                document.body.scrollTop ||
                0) > 400;
    }
    get paginationPages(): PaginationPage[] {
        const pages: PaginationPage[] = [];
        const total = this.totalPages;

        // Always show page 1
        pages.push({ label: '1', value: 1 });

        if (total <= 7) {
            // Show all pages if total is small
            for (let i = 2; i <= total; i++) {
                pages.push({ label: String(i), value: i });
            }
        } else {
            // Show current page and surrounding pages
            if (this.currentPage > 3) {
                pages.push({ label: '...', value: 'skip1', disabled: true });
            }

            const start = Math.max(2, this.currentPage - 1);
            const end = Math.min(total - 1, this.currentPage + 1);

            for (let i = start; i <= end; i++) {
                pages.push({ label: String(i), value: i });
            }

            if (this.currentPage < total - 2) {
                pages.push({ label: '...', value: 'skip2', disabled: true });
            }

            pages.push({ label: String(total), value: total });
        }

        pages.push({ label: '»', value: 'next' });

        return pages;
    }

    ngOnInit(): void {
        // Component initialization
        this.activeSection = 'qa-legal';
        this.loadQAFields();
        this.loadQAs();
    }

    /**
     * Load danh sách QA Fields từ API
     */
    loadQAFields(): void {
        this.landingQAService.getAllQAFields()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe({
                next: (fields: QAField[]) => {
                    this.qaFields = fields;
                    // Cập nhật categories với dữ liệu từ API
                    this.updateCategoriesFromAPI(fields);
                },
                error: (error) => {
                    console.error('Error loading QA fields:', error);
                }
            });
    }

    /**
     * Load danh sách QAs từ API
     */
    loadQAs(): void {
        this.isLoading = true;
        const fieldId = this.currentCategory === '' ? undefined : this.currentCategory;
        const fieldName = this.currentCategory === '' ? undefined : this.currentCategory;

        this.landingQAService.getPublishedQAs(
            this.searchQuery || undefined,
            undefined, // topic
            fieldId && !isNaN(Number(fieldId)) ? Number(fieldId) : undefined, // fieldId nếu là số
            fieldName && isNaN(Number(fieldName)) ? fieldName : undefined, // fieldName nếu không phải số
            this.currentPage,
            this.pageSize
        )
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe({
                next: (response: QAListResponse) => {
                    this.qaList = response.results;
                    this.totalQAs = response.count;
                    this.isLoading = false;
                },
                error: (error) => {
                    console.error('Error loading QAs:', error);
                    this.isLoading = false;
                }
            });
    }

    /**
     * Cập nhật categories từ API data
     */
    updateCategoriesFromAPI(fields: QAField[]): void {
        // Giữ lại category "Tất cả" ở đầu
        this.categories = [
            // { id: 'all', name: 'Tất cả', count: this.totalQAs, description: 'Tất cả câu hỏi thuộc các lĩnh vực' }
        ];

        // Thêm các field từ API
        fields.forEach(field => {
            this.categories.push({
                id: field.id?.toString() || field.name,
                name: field.name,
                count: field.qa_count || 0,
                description: field.description
            });
        });
    }

    setCategory(cat: string): void {
        this.currentCategory = cat;
        this.currentPage = 1;
        this.loadQAs(); // Reload QAs khi thay đổi category
    }

    triggerSearch(): void {
        this.currentPage = 1;
        this.loadQAs(); // Reload QAs với từ khóa tìm kiếm mới
    }

    goToPage(page: number | string): void {
        if (page === 'next') {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            } else {
                this.currentPage = 1;
            }
        } else if (typeof page === 'number') {
            this.currentPage = page;
        }
        this.loadQAs(); // Reload QAs khi thay đổi trang
    }

    onPageSizeChange(): void {
        this.currentPage = 1;
        this.loadQAs(); // Reload QAs với page size mới
    }

    openModal(item: QAItem | QA): void {
        // Nếu item là từ API (QA), gọi API để lấy chi tiết
        if ('id' in item && typeof item.id === 'number') {
            this.loadQADetail(item.id);
        } else {
            // Nếu item là dữ liệu cũ (QAItem), hiển thị trực tiếp
            this.selectedItem = item as QAItem;
            this.showModal = true;
        }
    }

    /**
     * Load chi tiết QA từ API
     */
    loadQADetail(qaId: number): void {
        this.landingQAService.getPublishedQAById(qaId)
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe({
                next: (qa: QA) => {
                    this.selectedQADetail = qa;
                    // Convert QA to QAItem format for modal
                    this.selectedItem = {
                        id: qa.id.toString(),
                        title: qa.title,
                        cat: qa.fields?.[0]?.name || 'Chưa phân loại',
                        date: new Date(qa.updated_at).toLocaleDateString('vi-VN'),
                        question: qa.question,
                        answer: qa.answer
                    };
                    this.showModal = true;
                },
                error: (error) => {
                    console.error('Error loading QA detail:', error);
                }
            });
    }

    closeModal(): void {
        this.showModal = false;
    }
    scrollTo(id: string): void {
        this.router.navigate([
            "/home"
        ], { fragment: id });
    }
    scrollToTop(): void {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }
}