<div class="clsc-file" *ngIf="fileData && !(showLoadingFile$ | async); else loadingTemplate">
  <section class="clsc-file__main">
    <header class="clsc-file__header">
      <div class="clsc-file__header-left">
        <h2 class="clsc-file__title">{{ fileData?.title || "Chi tiết file" }}</h2>
        <p class="clsc-file__subtitle d-flex align-items-center">
          Chi tiết tài liệu
        </p>
      </div>
      <div class="clsc-file__header-right">
        <button
          type="button"
          class="clsc-file__close-btn btn btn-icon btn-ghost-secondary rounded-circle"
          (click)="closeShowFile()"
        >
          <span data-feather="x" size="20"></span>
        </button>
      </div>
    </header>

    <div class="clsc-file__body">
      <div class="clsc-file__contents">
        <div [innerHTML]="fileData?.toan_van | safe"></div>
      </div>
    </div>
  </section>
</div>

<ng-template #loadingTemplate>
  <div class="d-flex flex-column align-items-center justify-content-center h-100">
    <app-commercial-loading
      width="120"
      spinnerSize="1.8"
      spinnerBW="0.18"
    ></app-commercial-loading>
  </div>
</ng-template>