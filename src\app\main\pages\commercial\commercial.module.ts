import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CoreCommonModule } from '@core/common.module';
import { RouterModule } from '@angular/router';
import { CommercialComponent } from './commercial.component';
import { CommercialChatComponent } from "./pages/commercial-chat/commercial-chat.component";
import { FormsModule } from "@angular/forms";
import { NgbDropdownModule, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { CommercialSearchComponent } from './commercial-search/commercial-search.component';
import { ViewDetailFileModule } from 'app/layout/components/view-detail-file/view-detail-file.module';
import { DetailFileComponent } from './pages/detail-file/detail-file.component';
import { CommercialLoadingComponent } from "./components/commercial-loading/commercial-loading.component";
import { PipeModule } from 'app/layout/components/pipe/pipe.module';

@NgModule({
  declarations: [
    CommercialComponent,
    CommercialChatComponent,
    CommercialSearchComponent,
    DetailFileComponent,
    CommercialLoadingComponent
  ],
  imports: [
    CoreCommonModule,
    CommonModule,
    FormsModule,
    RouterModule,
    NgbDropdownModule,
    NgbModalModule,
    ViewDetailFileModule,
    PipeModule
  ],
  exports: [CommercialComponent]
})
export class CommercialModule {}
