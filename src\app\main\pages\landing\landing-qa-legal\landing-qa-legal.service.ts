import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "environments/environment";
import { Observable } from "rxjs";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";

export interface QAField {
    id: number | null;
    name: string;
    description: string;
    qa_count?: number;
}

export interface QA {
    id: number;
    title: string;
    question: string;
    answer: string;
    keywords?: string;
    topic?: string;
    fields?: QAField[];
    status: string;
    updated_at: string;
    created_at: string;
}

export interface QAListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: QA[];
}

@Injectable({
    providedIn: "root",
})
export class LandingQAService {

    constructor(private http: HttpClient) { }

    // ============ QAField APIs ============

    /**
     * <PERSON><PERSON>y danh sách tất cả QA fields (bao gồm option "Tất cả" ở đầu)
     */
    getAllQAFields(): Observable<QAField[]> {
        const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
        return this.http.get<QAField[]>(
            `${environment.apiUrl}/cms/public/qa_field/`,
            { headers }
        );
    }

    /**
     * Lấy chi tiết một QA field theo ID
     */
    getQAFieldById(id: number): Observable<QAField> {
        const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
        return this.http.get<QAField>(
            `${environment.apiUrl}/cms/public/qa_field/${id}/`,
            { headers }
        );
    }

    /**
     * Tạo mới QA field (chỉ admin)
     */
    createQAField(body: Partial<QAField>): Observable<QAField> {
        const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
        return this.http.post<QAField>(
            `${environment.apiUrl}/cms/public/qa_field/`,
            body,
            { headers }
        );
    }

    /**
     * Cập nhật QA field (chỉ admin)
     */
    updateQAField(id: number, body: Partial<QAField>): Observable<QAField> {
        const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
        return this.http.put<QAField>(
            `${environment.apiUrl}/cms/public/qa_field/${id}/`,
            body,
            { headers }
        );
    }

    /**
     * Cập nhật một phần QA field (chỉ admin)
     */
    partialUpdateQAField(id: number, body: Partial<QAField>): Observable<QAField> {
        const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
        return this.http.patch<QAField>(
            `${environment.apiUrl}/cms/public/qa_field/${id}/`,
            body,
            { headers }
        );
    }

    /**
     * Xóa QA field (chỉ admin)
     */
    deleteQAField(id: number): Observable<any> {
        const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
        return this.http.delete<any>(
            `${environment.apiUrl}/cms/public/qa_field/${id}/`,
            { headers }
        );
    }

    // ============ Published QA APIs ============

    /**
     * Lấy danh sách Q&A đã published (hỗ trợ search, filter theo topic và field)
     * @param q - Từ khóa tìm kiếm (tìm trong title, question, answer, keywords)
     * @param topic - Lọc theo topic
     * @param fieldId - Lọc theo QA field ID (null hoặc không truyền để lấy tất cả)
     * @param page - Số trang
     * @param pageSize - Số lượng items mỗi trang
     */
    getPublishedQAs(
        q?: string,
        topic?: string,
        fieldId?: number | string,
        fieldName?: string,
        page?: number,
        pageSize?: number
    ): Observable<QAListResponse> {
        const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
        let params = new HttpParams();

        if (q) {
            params = params.set("q", q);
        }
        if (topic) {
            params = params.set("topic", topic);
        }
        if (fieldId !== undefined && fieldId !== null && fieldId !== "") {
            params = params.set("field_id", fieldId.toString());
        }
        if (fieldName) {
            if (fieldName === "Tất cả") {
                fieldName = "";
            }
            params = params.set("field_name", fieldName);
        }
        if (page) {
            params = params.set("page", page.toString());
        }
        if (pageSize) {
            params = params.set("page_size", pageSize.toString());
        }

        return this.http.get<QAListResponse>(
            `${environment.apiUrl}/cms/public/published-qa`,
            { params, headers }
        );
    }

    /**
     * Lấy chi tiết một Q&A đã published theo ID
     */
    getPublishedQAById(id: number): Observable<QA> {
        const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
        return this.http.get<QA>(
            `${environment.apiUrl}/cms/public/published-qa/${id}/`,
            { headers }
        );
    }
}