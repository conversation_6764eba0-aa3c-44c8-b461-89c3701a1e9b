import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule, Routes } from "@angular/router";
import { CoreCommonModule } from "@core/common.module";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { ContentHeaderModule } from "app/layout/components/content-header/content-header.module";
import { BreadcrumbModule } from "app/layout/components/content-header/breadcrumb/breadcrumb.module";
import { Ng2FlatpickrModule } from "ng2-flatpickr";
import { PipeModule } from "app/layout/components/pipe/pipe.module";
import { ChipsModule } from "primeng/chips";
import { MultiSelectModule } from "primeng/multiselect";
import { PackageComponent } from "./package.component";
const routes: Routes = [
  { path: "", component: PackageComponent },
];

@NgModule({
  declarations: [
    PackageComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    NgSelectModule,
    CoreCommonModule,
    NgxDatatableModule,
    ContentHeaderModule,
    NgbModule,
    BreadcrumbModule,
    ReactiveFormsModule,
    FormsModule,
    Ng2FlatpickrModule,
    PipeModule,
    MultiSelectModule,
    ChipsModule,
  ],
})
export class PackageModule {}
