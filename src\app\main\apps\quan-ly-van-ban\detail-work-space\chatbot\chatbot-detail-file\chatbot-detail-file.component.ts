import { Component, EventEmitter, Input, Output, ViewEncapsulation, OnChanges, SimpleChanges, HostListener, OnInit, OnDestroy, ElementRef } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ViewDetailFileService } from "app/layout/components/view-detail-file/view-detail-file.service";
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-chatbot-detail-file',
  templateUrl: './chatbot-detail-file.component.html',
  styleUrls: ['./chatbot-detail-file.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ChatbotDetailFileComponent implements OnChanges, OnInit, OnDestroy {
  @Input() file: any;
  @Input() showHeader: boolean = true;
  @Output() close = new EventEmitter<void>();
  @Output() openLinkedFile = new EventEmitter<any>(); 
  @Output() save = new EventEmitter<void>(); 
  public safeHtmlContent: SafeHtml = '';
  private _unsubscribeAll = new Subject<any>();

  constructor(
    private sanitizer: DomSanitizer,
    private viewDetailFileService: ViewDetailFileService,
    private el: ElementRef // PHẢI CÓ DÒNG NÀY (Lỗi Property 'el' do thiếu ở đây)
  ) {}
  @HostListener('click', ['$event'])
  onClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const anchor = target.closest('a');
    
    if (!anchor) return;

    let href = anchor.getAttribute('href');
    if (!href) return;

    // 1. Xử lý tiền tố unsafe do Angular
    if (href.startsWith('unsafe:')) {
      href = href.replace('unsafe:', '');
    }

    // 2. Nếu là link nội bộ (cuộn trang)
    if (href.startsWith('#')) {
      event.preventDefault();
      event.stopPropagation();
      this.scrollToElement(href.substring(1));
      return;
    }

    // 3. Nếu là link hệ thống (legal:, user:, upload:...)
    const systemProtocols = ['legal:', 'local:', 'user:', 'upload:'];
    if (systemProtocols.some(p => href.startsWith(p))) {
      event.preventDefault();
      event.stopPropagation();
      let docId = href;

      // Loại bỏ tiền tố 'legal:' để lấy ID sạch (vì Parent Component không tự xóa 'legal:')
      if (docId.startsWith('legal:')) {
        docId = docId.replace('legal:', '');
      }
      const fileData = {
        id: docId, 
        name: anchor.innerText || 'Văn bản liên quan'
      };
      this.openLinkedFile.emit(fileData); 
    }
  }
  ngOnInit(): void {
    // Lắng nghe lệnh scroll từ ListDocument
    this.viewDetailFileService.clauseId2
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((id) => {
        if (id) {
          console.log('[DEBUG] Nhận lệnh scroll tới:', id);
          this.scrollToElement(id);
        }
      });
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  scrollToElement(targetId: string) {
    if (!targetId) return;

    setTimeout(() => {
      const container = this.el.nativeElement.querySelector('.document-body');
      if (!container) return;
      console.log('[DEBUG-SCROLL] Đang tìm kiếm mục tiêu:', targetId);
      let element = container.querySelector(`[id="${targetId}"]`);

      if (!element && this.file?.terms) {
          // Tra cứu UUID để lấy thông tin Title chữ
          const foundClause = this.file.terms.find(t => 
              t.clause_id === targetId || t.id?.toString() === targetId || t.term_id === targetId
          );

          if (foundClause) {
            // Làm sạch tiêu đề (bỏ dấu ** của markdown nếu có)
            const rawTitle = (foundClause.title || foundClause.name || '').replace(/\*\*/g, '').trim();
            const titleToSearch = rawTitle.toLowerCase();
            
            console.log('[DEBUG-SCROLL] UUID khớp với tiêu đề:', rawTitle);
            const targets = container.querySelectorAll('h1, h2, h3, h4, h5, p, b, strong, a[name]');
            for (let i = 0; i < targets.length; i++) {
              const el = targets[i] as HTMLElement;
              const elText = el.innerText.trim().toLowerCase();
              const elName = el.getAttribute('name')?.toLowerCase() || '';
              // Nếu nội dung chữ của thẻ khớp với tiêu đề hoặc thẻ <a> có name khớp
              if (elText.includes(titleToSearch) || titleToSearch.includes(elText) || elName === titleToSearch) {
                element = el;
                break;
              }
            }
          }
        }

        // BƯỚC 3: THỰC HIỆN CUỘN
        if (element) {
            console.log('✅ [SCROLL-SUCCESS] Đã tìm thấy thẻ:', element);
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Hiệu ứng highlight màu vàng nhạt để người dùng dễ thấy
            const originalBg = element.style.backgroundColor;
            element.style.backgroundColor = "#fff9c4";
            element.style.transition = "background-color 0.5s ease";
            setTimeout(() => { element.style.backgroundColor = originalBg || "transparent"; }, 2000);
        } else {
            console.warn('❌ [SCROLL-FAILED] Không tìm thấy thẻ nào cho ID:', targetId);
        }
    }, 500);
  }
  
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['file']) {
      let content = this.file?.toan_van || '';
      content = content.replace(/onclick="[^"]*"/g, "");
      content = content.replace(/href=["'](legal:[^"']+|local:[^"']+|user:[^"']+|upload:[^"']+)["']/g, (match, url) => {
          return `href="${url}" class="legal-link"`; 
      });
      this.safeHtmlContent = this.sanitizer.bypassSecurityTrustHtml(content);
    }
  }

  onSaveClick() { if (this.save) this.save.emit(); }
}