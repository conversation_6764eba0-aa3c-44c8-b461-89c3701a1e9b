<div class="modal-header">
  <h4 class="modal-title">Chọn không gian dự án</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="cancel()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <div class="workspace-selection-container">
    <!-- Thông tin file được chọn -->
    <div class="selected-files-info mb-3">
     
        <strong>Đ<PERSON> chọn {{ selectedFiles.length }} tài liệu</strong> để lưu vào không gian dự án
      
    </div>

    <!-- Tìm kiếm workspace -->
    <div class="search-workspace mb-3">
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          placeholder="Tìm kiếm không gian dự án..."
          [(ngModel)]="searchWorkspace"
          (input)="onSearchChange()"
        />
        <div class="input-group-append">
          <span class="input-group-text">
            <i class="feather icon-search"></i>
          </span>
        </div>
      </div>
    </div>

    <!-- Nút thao tác -->
    <div class="action-buttons mb-1 d-flex justify-content-between">
      <div [style.visibility]="listWorkSpace.length === 0 ? 'hidden' : 'visible'" class="form-check  pt-10px">
        <input
          class="form-check-input"
          type="checkbox"
          id="selectAllCheckbox"
          [checked]="isAllSelected()"
          [indeterminate]="isIndeterminate()"
          (change)="toggleSelectAll($event)">
        <label class="form-check-label font-weight-bolder font-14 pt-2px "r="selectAllCheckbox">
          Chọn tất cả không gian dự án
        </label>
      </div>
      <button class="btn btn-sm btn-primary p-1 font-14"    (click)="addWorkSpace()">
        <i class="feather icon-plus"></i>
        Tạo mới không gian dự án
      </button>
    </div>

    <!-- Danh sách workspace -->
    <div class="workspace-list" *ngIf="!isLoading">
      <div class="workspace-list-container" style="max-height: 400px; overflow-y: auto;">
        <div
          class="workspace-item d-flex align-items-center border rounded mb-1"
          *ngFor="let workspace of listWorkSpace"
          [class.selected]="isWorkspaceSelected(workspace)"
        >
<div class="form-check d-flex align-items-center p-1 me-3" style="min-width: 20px;">
  <input
    class="form-check-input m-0"
    type="checkbox"
    [id]="'workspace-' + workspace.id"
    [checked]="isWorkspaceSelected(workspace)"
    (change)="onWorkspaceToggle(workspace, $event)"
  />
</div>

<label
  class="workspace-info flex-grow-1 ml-1 cursor-pointer label-custom"
  [for]="'workspace-' + workspace.id"
>
  {{ workspace.name }}
</label>

        </div>

        <!-- Không có dữ liệu -->
        <div class="no-data text-center py-4" *ngIf="listWorkSpace.length === 0">
          <p class="text-muted">Không tìm thấy không gian dự án nào</p>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div class="loading text-center py-4" *ngIf="isLoading">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Đang tải...</span>
      </div>
    </div>

    <!-- Thông tin workspace được chọn -->
    <!-- <div class="selected-workspaces-info mt-3" *ngIf="selectedWorkspaces.length > 0">
      <p class="mb-2">
        <strong>Đã chọn {{ selectedWorkspaces.length }} không gian dự án:</strong>
      </p>
      <div class="selected-workspace-tags">
        <span 
          class="badge badge-primary me-2 mb-2"
          *ngFor="let workspace of selectedWorkspaces"
        >
          {{ workspace.name }}
          <button 
            type="button" 
            class="btn-close btn-close-white ms-1"
            (click)="onWorkspaceToggle(workspace, {target: {checked: false}})"
          >
            &times;
          </button>
        </span>
      </div>
    </div> -->
  </div>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">
    Hủy
  </button>
  <button
    type="button"
    class="btn btn-outline-primary me-2"
    (click)="saveToDefaultWorkspace()"
    ngbTooltip="Xem danh sách tài liệu ở mục So sánh và Chuyển đổi"
    >
    Lưu ngoài không gian dự án
  </button>
  <button
    type="button"
    class="btn btn-primary"
    (click)="saveToSelectedWorkspaces()"
    [disabled]="selectedWorkspaces.length === 0"
  >
    Lưu vào {{ selectedWorkspaces.length }} không gian dự án
  </button>
</div>
<ng-template #modalWorkSpace let-modal>
  <app-work-space-control
    (destroyed)="onChildDestroyed()"
    [title]="title"
    [modal]="modal"
    [type]="type"
    [row]="row"
  ></app-work-space-control>
</ng-template>