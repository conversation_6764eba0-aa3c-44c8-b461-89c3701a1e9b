<div class="container p-0">
  <app-breadcrumb [breadcrumb]="breadcrumbDefault"></app-breadcrumb>

  <div class="card">
    <div class="card-header">
      <div class="d-flex align-items-center flex-wrap gap-1">
        <div class="mr-2">
          <label class="mb-0 small d-block">ID gói</label>
          <input
            type="text"
            class="form-control"
            placeholder="ID gói"
            [(ngModel)]="filterId"
            (ngModelChange)="applyFilter({ id: filterId })"
          />
        </div>
        <div class="mr-2">
          <label class="mb-0 small d-block">Kiểu gói</label>
          <ng-select
            [items]="[
              { label: 'Mặc định', value: 'true' }
            ]"
            bindLabel="label"
            bindValue="value"
            [(ngModel)]="filterIsDefault"
            (ngModelChange)="applyFilter({ is_default: filterIsDefault })"
            placeholder="Kiểu gói"
          >
          </ng-select>
        </div>
        <div class="mr-2">
          <label class="mb-0 small d-block">Kiểu upload</label>
          <ng-select
            [items]="fileTypeOptions"
            bindLabel="label"
            bindValue="value"
            [(ngModel)]="filterUploadType"
            (ngModelChange)="applyFilter({ upload_type: filterUploadType })"
            placeholder="Kiểu upload"
          >
          </ng-select>
        </div>
        <div class="mr-2">
          <label class="mb-0 small d-block">Kiểu download</label>
          <ng-select
            [items]="fileTypeOptions"
            bindLabel="label"
            bindValue="value"
            [(ngModel)]="filterDownloadType"
            (ngModelChange)="applyFilter({ download_type: filterDownloadType })"
            placeholder="Kiểu download"
          >
          </ng-select>
        </div>
      </div>

      <div>
        <button 
          class="btn btn-primary-theme"
          rippleEffect 
          (click)="openModal(packageModal)">
          <span data-feather="plus" class="mr-25"></span>
          Thêm gói bán
        </button>
      </div>
    </div>

    <div class="card-body">
      <ngx-datatable
        #tablePackage
        class="bootstrap core-bootstrap cursor"
        [columnMode]="ColumnMode.force"
        [rows]="packages"
        [loadingIndicator]="loading"
        [columnMode]="'force'"
        [headerHeight]="50"
        [footerHeight]="50"
        [rowHeight]="'auto'"
        [limit]="pageSize"
        [count]="total"
        [offset]="page - 1"
        [externalPaging]="true"
        (page)="pageChange($event.offset + 1)"
        [reorderable]="false"
      >
        <ngx-datatable-column
          [width]="50"
          [resizeable]="false"
          [sortable]="false"
          [draggable]="false"
          [canAutoResize]="false"
        >
          <ng-template let-row="row" let-expanded="expanded" ngx-datatable-cell-template>
            <a
              href="javascript:void(0)"
              [class.datatable-icon-right]="!expanded"
              [class.datatable-icon-down]="expanded"
              title="Mở rộng/Đóng"
              (click)="onDetailToggle(row)"
            ></a>
          </ng-template>
        </ngx-datatable-column>
        
        <ngx-datatable-row-detail [rowHeight]="'auto'">
          <ng-template let-row="row" let-expanded="expanded" ngx-datatable-row-detail-template>
            <div class="px-3 pt-1 border-top">
              <div class="row">
                <div class="col-md-12">
                  <div><b>Mô tả:</b> {{ row.description }}</div>
                </div>
                <div class="col-md-4">
                  <div class="mt-2"><b>Số file/tải lên:</b> {{ row.max_num_doc_per_upload | number }} files</div>
                  <div class="mt-2"><b>Số trang document:</b> {{ row.total_document_page | number }} trang</div>
                  <div class="mt-2"><b>Kích thước file tối đa:</b> {{ row.max_file_size_per_upload | number }} Kb</div>
                </div>
                <div class="col-md-4">
                  <div class="mt-2"><b>Giới hạn request:</b> {{ row.limit_request | number }} request</div>
                  <div class="mt-2"><b>Token GPT:</b> {{ row.total_llm_token | number }} token</div>
                </div>
                <div class="col-md-4">
                  <div class="mt-2"><b>Giá tháng:</b> {{ row.monthly_price | number }} VNĐ</div>
                  <div class="mt-2"><b>Giá năm:</b> {{ row.yearly_price | number }} VNĐ</div>
                </div>
              </div>
            </div>
          </ng-template>
        </ngx-datatable-row-detail>

        <ngx-datatable-column name="ID" prop="id" [width]="100" [sortable]="false" [draggable]="false"></ngx-datatable-column>
        <ngx-datatable-column name="Tải lên" prop="upload_type" [sortable]="false" [draggable]="false"></ngx-datatable-column>
        <ngx-datatable-column name="Tải xuống" prop="download_type" [sortable]="false" [draggable]="false"></ngx-datatable-column>
        <ngx-datatable-column name="Mặc định" prop="is_default" [width]="100" [sortable]="false" [draggable]="false">
          <ng-template let-row="row" ngx-datatable-cell-template>
            <span *ngIf="row.is_default" class="badge bg-success">
              <i data-feather="check"></i>
            </span>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Tính năng" [width]="240" [sortable]="false" [draggable]="false">
          <ng-template let-row="row" ngx-datatable-cell-template>
            <span class="mr-1 d-inline-block min-width-36px">
              <ng-container *ngIf="row.enable_chatbot; else chatbotPlaceholder">
                <span
                  class="badge badge-light-info"
                  title="Chatbot"
                  ngbTooltip="Chatbot"
                >
                  <i data-feather="message-square"></i>
                </span>
              </ng-container>
              <ng-template #chatbotPlaceholder>
                <span
                  class="badge badge-light-info invisible w-100"
                >
                  <i data-feather="message-square"></i>
                </span>
              </ng-template>
            </span>
            <span class="mr-1 d-inline-block min-width-36px">
              <ng-container *ngIf="row.enable_term_compare; else termComparePlaceholder">
                <span
                  class="badge badge-light-primary"
                  title="So sánh điều khoản"
                  ngbTooltip="So sánh điều khoản"
                >
                  <i data-feather="file-text"></i>
                </span>
              </ng-container>
              <ng-template #termComparePlaceholder>
                <span
                  class="badge badge-light-primary invisible w-100"
                >
                  <i data-feather="file-text"></i>
                </span>
              </ng-template>
            </span>
            <span class="mr-1 d-inline-block min-width-36px">
              <ng-container *ngIf="row.enable_document_writting; else writtingPlaceholder">
                <span
                  class="badge badge-light-warning"
                  title="Soạn thảo"
                  ngbTooltip="Soạn thảo"
                >
                  <i data-feather="edit"></i>
                </span>
              </ng-container>
              <ng-template #writtingPlaceholder>
                <span
                  class="badge badge-light-warning invisible w-100"
                >
                  <i data-feather="edit"></i>
                </span>
              </ng-template>
            </span>
            <span class="mr-1 d-inline-block min-width-36px">
              <ng-container *ngIf="row.enable_document_summarize; else summarizePlaceholder">
                <span
                  class="badge badge-light-success"
                  title="Tóm tắt"
                  ngbTooltip="Tóm tắt"
                >
                  <i data-feather="book-open"></i>
                </span>
              </ng-container>
              <ng-template #summarizePlaceholder>
                <span
                  class="badge badge-light-success invisible w-100"
                >
                  <i data-feather="book-open"></i>
                </span>
              </ng-template>
            </span>
            <span class="mr-1 d-inline-block min-width-36px">
              <ng-container *ngIf="row.enable_document_compare; else comparePlaceholder">
                <span
                  class="badge badge-light-danger"
                  title="So sánh"
                  ngbTooltip="So sánh"
                >
                  <i data-feather="code"></i>
                </span>
              </ng-container>
              <ng-template #comparePlaceholder>
                <span
                  class="badge badge-light-danger invisible w-100"
                >
                  <i data-feather="code"></i>
                </span>
              </ng-template>
            </span>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="Hành động" [width]="120" [sortable]="false" [draggable]="false">
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="d-flex align-items-center">
              <div ngbDropdown container="body">
                <a
                  ngbDropdownToggle
                  href="javascript:void(0);"
                  class="hide-arrow"
                  id="dropdownBrowserState"
                  data-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  <i data-feather="more-vertical" class="text-primary cursor-pointer mr-50"></i>
                </a>
                <div ngbDropdownMenu class="dropdown-menu-right" aria-labelledby="dropdownBrowserState">
                  <a href="javascript:void(0)" ngbDropdownItem class="d-flex align-items-center" (click)="openModal(packageModal, row)">
                    <i data-feather="edit-2" class="mr-50"></i> Sửa gói bán
                  </a>
                  <!-- <a href="javascript:void(0)" ngbDropdownItem class="d-flex align-items-center" (click)="deletePackage(row)">
                    <i data-feather="trash" class="mr-50"></i> Xóa gói bán
                  </a> -->
                </div>
              </div>
            </div>
          </ng-template>
        </ngx-datatable-column>

      </ngx-datatable>
    </div>

  </div>
</div>

<ng-template #packageModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">{{ editingPackage ? 'Chỉnh sửa Package' : 'Thêm Package' }}</h5>
    <button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body" tabindex="0" ngbAutofocus>
    <form [formGroup]="packageForm" (ngSubmit)="submitPackage()">
      <div class="modal-body row">
        <div class="col-md-6 mb-2">
          <label class="form-label">ID <span class="text-danger">*</span></label>
          <input formControlName="id" class="form-control" [readonly]="editingPackage" placeholder="Nhập ID" />
        </div>
        <div class="col-md-6 mb-2">
          <div class="custom-control custom-control-primary custom-switch">
            <p class="mb-50">Mặc định</p>
            <input 
              type="checkbox" 
              formControlName="is_default" 
              [checked]="packageForm.get('is_default').value" 
              class="custom-control-input" 
              id="is_default"
              disabled
            />
            <label class="custom-control-label" for="is_default"></label>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <label class="form-label">Tải lên <span class="text-danger">*</span></label>
          <ng-select 
            [items]="fileTypeOptions"
            [multiple]="true"
            [closeOnSelect]="false"
            formControlName="upload_type"
            bindLabel=""
            placeholder="Chọn loại file"
            required>
          </ng-select>
        </div>
        <div class="col-md-6 mb-2">
          <label class="form-label">Tải xuống <span class="text-danger">*</span></label>
          <ng-select 
            [items]="fileTypeOptions"
            [multiple]="true"
            [closeOnSelect]="false"
            formControlName="download_type"
            bindLabel=""
            placeholder="Chọn loại file"
            required>
          </ng-select>
        </div>
        <div class="col-md-4 mb-2">
          <label class="form-label">Kích thước file tối đa <span class="text-danger">*</span></label>
          <div class="input-group input-group-merge">
            <input
              type="number"
              class="form-control"
              placeholder="0"
              aria-label="0"
              aria-describedby="max_file_size_per_upload"
              formControlName="max_file_size_per_upload"
              required min="0"
            />
            <div class="input-group-append">
              <span class="input-group-text" id="max_file_size_per_upload">Kb</span>
            </div>
          </div>
        </div>
        <div class="col-md-4 mb-2">
          <label class="form-label">Số file/tải lên <span class="text-danger">*</span></label>
          <div class="input-group input-group-merge">
            <input
              type="number"
              class="form-control"
              placeholder="0"
              aria-label="0"
              aria-describedby="max_num_doc_per_upload"
              formControlName="max_num_doc_per_upload"
              required
              min="0"
            />
            <div class="input-group-append">
              <span class="input-group-text" id="max_num_doc_per_upload">File</span>
            </div>
          </div>
        </div>
        <div class="col-md-4 mb-2">
          <label class="form-label">Số trang tài liệu <span class="text-danger">*</span></label>
          <div class="input-group input-group-merge">
            <input
              type="number"
              class="form-control"
              placeholder="0"
              aria-label="0"
              aria-describedby="total_document_page"
              formControlName="total_document_page"
              min="0"
              required
            />
            <div class="input-group-append">
              <span class="input-group-text" id="total_document_page">Trang</span>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <label class="form-label">Giới hạn request <span class="text-danger">*</span></label>
          <div class="input-group input-group-merge">
            <input
              type="number"
              class="form-control"
              placeholder="0"
              aria-label="0"
              aria-describedby="limit_request"
              formControlName="limit_request"
              required
              min="0"
            />
            <div class="input-group-append">
              <span class="input-group-text" id="limit_request">Req</span>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <label class="form-label">Token GPT <span class="text-danger">*</span></label>
          <div class="input-group input-group-merge">
            <input
              type="number"
              class="form-control"
              placeholder="0"
              aria-label="0"
              aria-describedby="total_llm_token"
              formControlName="total_llm_token"
              min="0"
              required
            />
            <div class="input-group-append">
              <span class="input-group-text" id="total_llm_token">Token</span>
            </div>
          </div>
        </div>
        <div class="col mb-2 d-flex align-items-center">
          <label class="form-label mb-0 mr-1">Chatbot</label>
          <div class="custom-control custom-control-primary custom-switch ms-auto">
            <input
              type="checkbox"
              class="custom-control-input"
              id="enable_chatbot"
              formControlName="enable_chatbot"
            />
            <label class="custom-control-label" for="enable_chatbot"></label>
          </div>
        </div>
        <div class="col mb-2 d-flex align-items-center">
          <label class="form-label mb-0 mr-1">So sánh điều khoản</label>
          <div class="custom-control custom-control-primary custom-switch ms-auto">
            <input
              type="checkbox"
              class="custom-control-input"
              id="enable_term_compare"
              formControlName="enable_term_compare"
            />
            <label class="custom-control-label" for="enable_term_compare"></label>
          </div>
        </div>
        <div class="col mb-2 d-flex align-items-center">
          <label class="form-label mb-0 mr-1">Soạn thảo</label>
          <div class="custom-control custom-control-primary custom-switch ms-auto">
            <input
              type="checkbox"
              class="custom-control-input"
              id="enable_document_writting"
              formControlName="enable_document_writting"
            />
            <label class="custom-control-label" for="enable_document_writting"></label>
          </div>
        </div>
        <div class="col mb-2 d-flex align-items-center">
          <label class="form-label mb-0 mr-1">Tóm tắt</label>
          <div class="custom-control custom-control-primary custom-switch ms-auto">
            <input
              type="checkbox"
              class="custom-control-input"
              id="enable_document_summarize"
              formControlName="enable_document_summarize"
            />
            <label class="custom-control-label" for="enable_document_summarize"></label>
          </div>
        </div>
        <div class="col mb-2 d-flex align-items-center">
          <label class="form-label mb-0 mr-1">So sánh</label>
          <div class="custom-control custom-control-primary custom-switch ms-auto">
            <input
              type="checkbox"
              class="custom-control-input"
              id="enable_document_compare"
              formControlName="enable_document_compare"
            />
            <label class="custom-control-label" for="enable_document_compare"></label>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <label class="form-label">Giá tháng <span class="text-danger">*</span></label>
          <div class="input-group input-group-merge">
            <input
              type="number"
              class="form-control"
              placeholder="0"
              aria-label="0"
              aria-describedby="monthly_price"
              formControlName="monthly_price"
              required
              min="0"
            />
            <div class="input-group-append">
              <span class="input-group-text" id="monthly_price">VNĐ</span>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <label class="form-label">Giá năm <span class="text-danger">*</span></label>
          <div class="input-group input-group-merge">
            <input
              type="number"
              class="form-control"
              placeholder="0"
              aria-label="0"
              aria-describedby="yearly_price"
              formControlName="yearly_price"
              required
              min="0"
            />
            <div class="input-group-append">
              <span class="input-group-text" id="yearly_price">VNĐ</span>
            </div>
          </div>
        </div>
        <div class="col-md-12 mb-2">
          <label class="form-label">Mô tả</label>
          <textarea formControlName="description" class="form-control"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary"
          [disabled]="packageForm.invalid">
          Lưu
        </button>
        <button type="button" class="btn btn-secondary" (click)="modalRef.dismiss()">Đóng</button>
      </div>
    </form>
  </div>
</ng-template>