<div class="full-table-container" [ngClass]="{ 'tim-kiem-mode': isTimKiemMode }">
  <div class="card mb-3">
    <div class="d-flex align-items-center justify-content-between mb-1">
      <div class="input-group input-group-merge w-25">
        <div class="input-group-prepend">
          <span class="input-group-text">
            <i data-feather="search" class="text-muted"></i>
          </span>
        </div>
        <input
          type="text"
          class="form-control"
          id="full-table-search"
          placeholder="Tìm kiếm"
          aria-label="Tìm kiếm"
          aria-describedby="full-table-search"
          [(ngModel)]="fullTableSearch"
          [ngModelOptions]="{ standalone: true }"
          (ngModelChange)="onSearchChange($event)"
        />
      </div>
      <button
        type="button"
        class="btn btn-outline-secondary btn-sm d-flex align-items-center"
        (click)="onToggleView()"
      >
        <img
          src="assets/images/icons/show-table-active.svg"
          alt="toggle"
          class="mr-50"
        />
        <b>Dạng danh sách</b>
      </button>
    </div>
    <div class="folder full-table-wrapper">
      <ngx-datatable
        #fullTableDatatable
        [rows]="getPaginatedDocuments()"
        [rowHeight]="45"
        class="bootstrap core-bootstrap"
        [columnMode]="isTimKiemMode ? ColumnMode.force : ColumnMode.standard"
        [headerHeight]="40"
        [footerHeight]="50"
        [scrollbarH]="!isTimKiemMode"
        [scrollbarV]="false"
        [limit]="pageSize"
        [count]="totalItemValue"
        [externalPaging]="true"
        (page)="onPageChange($event)"
        [offset]="page - 1"
        [selected]="selectedTableRows"
        [selectionType]="SelectionType.checkbox"
        (select)="onTableSelect($event)"
        (activate)="onTableActivate($event)"
        [rowClass]="getRowClass"
      >
      <ngx-datatable-column
        [width]="50"
        [minWidth]="50"
        [resizeable]="false"
        [sortable]="false"
        [draggable]="false"
        [canAutoResize]="false"
      >
        <ng-template ngx-datatable-header-template>
          <div class="custom-control custom-checkbox">
            <input
              type="checkbox"
              class="custom-control-input"
              id="selectAllDocumentsFull"
              [checked]="isAllDocumentsSelected()"
              (change)="onHeaderCheckboxChange($event)"
            />
            <label
              class="custom-control-label"
              for="selectAllDocumentsFull"
            ></label>
          </div>
        </ng-template>
        <ng-template
          ngx-datatable-cell-template
          let-value="value"
          let-row="row"
          let-isSelected="isSelected"
          let-onCheckboxChangeFn="onCheckboxChangeFn"
        >
          <div class="custom-control custom-checkbox">
            <input
              type="checkbox"
              class="custom-control-input"
              [id]="'full-doc-' + row.id"
              [checked]="isSelected"
              (change)="onCheckboxChangeFn($event)"
            />
            <label
              class="custom-control-label"
              [for]="'full-doc-' + row.id"
            ></label>
          </div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="Tên văn bản"
        [sortable]="false"
        [width]="isTimKiemMode ? undefined : 220"
        [minWidth]="220"
        [flexGrow]="isTimKiemMode ? 3 : 0"
        [resizeable]="false"
      >
        <ng-template
          let-row="row"
          ngx-datatable-cell-template
        >
          <span
            [ngbTooltip]="row.apiNode?.thuoc_tinh?.ten_day_du"
            container="body"
            style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: block; cursor: pointer;"
            (click)="onDocumentNameClick(row)"
          >
            {{ getTruncatedDocumentName(row) }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="Loại văn bản"
        prop="loai_van_ban"
        [width]="isTimKiemMode ? undefined : 160"
        [minWidth]="160"
        [flexGrow]="isTimKiemMode ? 1 : 0"
        [sortable]="true"
        [resizeable]="false"
      >
        <ng-template
          let-row="row"
          ngx-datatable-cell-template
        >
          {{ row.apiNode?.thuoc_tinh?.loai_van_ban || "" }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="Số hiệu"
        prop="so_hieu"
        [width]="isTimKiemMode ? undefined : 140"
        [minWidth]="140"
        [flexGrow]="isTimKiemMode ? 1 : 0"
        [sortable]="true"
        [resizeable]="false"
      >
        <ng-template
          let-row="row"
          ngx-datatable-cell-template
        >
          {{ row.apiNode?.thuoc_tinh?.so_hieu || "" }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="Cơ quan ban hành"
        prop="co_quan_ban_hanh"
        [width]="isTimKiemMode ? undefined : 200"
        [minWidth]="200"
        [flexGrow]="isTimKiemMode ? 2 : 0"
        [sortable]="true"
        [resizeable]="false"
      >
        <ng-template
          let-row="row"
          ngx-datatable-cell-template
        >
          {{ row.apiNode?.thuoc_tinh?.co_quan_ban_hanh || "" }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="Ngày ban hành"
        prop="ngay_ban_hanh"
        [width]="isTimKiemMode ? undefined : 160"
        [minWidth]="160"
        [flexGrow]="isTimKiemMode ? 1 : 0"
        [sortable]="true"
        [comparator]="dateComparator"
        [resizeable]="false"
      >
        <ng-template
          let-row="row"
          ngx-datatable-cell-template
        >
          {{
            row.apiNode?.thuoc_tinh?.ngay_ban_hanh
              | date : "dd/MM/yyyy" : "+0000"
          }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="Ngày có hiệu lực"
        prop="ngay_co_hieu_luc"
        [width]="isTimKiemMode ? undefined : 200"
        [minWidth]="200"
        [flexGrow]="isTimKiemMode ? 1 : 0"
        [sortable]="true"
        [comparator]="dateComparator"
        [resizeable]="false"
      >
        <ng-template
          let-row="row"
          ngx-datatable-cell-template
        >
          {{
            row.apiNode?.thuoc_tinh?.ngay_co_hieu_luc
              | date : "dd/MM/yyyy" : "+0000"
          }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="Trạng thái hiệu lực"
        prop="tinh_trang_hieu_luc"
        [width]="isTimKiemMode ? undefined : 220"
        [minWidth]="220"
        [flexGrow]="isTimKiemMode ? 2 : 0"
        [sortable]="true"
        [resizeable]="false"
      >
        <ng-template
          let-row="row"
          ngx-datatable-cell-template
        >
          <span
            class="badge badge-pill"
            [ngClass]="{
              'badge-light-success':
                row.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc ===
                'Còn hiệu lực',
              'badge-light-warning':
                row.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc ===
                'Hết hiệu lực một phần',
              'badge-light-danger':
                row.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc ===
                'Hết hiệu lực toàn bộ',
              'badge-light-info': ![
                'Còn hiệu lực',
                'Hết hiệu lực một phần',
                'Hết hiệu lực toàn bộ'
              ].includes(row.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc || '')
            }"
          >
            {{ row.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc || "" }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      </ngx-datatable>
    </div>
  </div>
</div>
