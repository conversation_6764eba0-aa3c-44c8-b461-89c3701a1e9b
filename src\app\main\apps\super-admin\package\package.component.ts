import { ViewEncapsulation, Component, OnInit, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import { PackageService } from './package.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ColumnMode } from '@swimlane/ngx-datatable';
import Swal from 'sweetalert2';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

@Component({
  selector: 'app-package',
  templateUrl: './package.component.html',
  styleUrls: ['./package.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class PackageComponent implements OnInit, OnDestroy {
  @ViewChild('tablePackage') tablePackage: any;
  public ColumnMode = ColumnMode;

  public breadcrumbDefault: object;

  packages: any[] = [];
  total: number = 0;
  page: number = 1;
  pageSize: number = 12;
  loading: boolean = false;

  // For modal
  packageForm: FormGroup;
  editingPackage: any = null;
  modalRef: any;

  public fileTypeOptions = ['docx', 'doc', 'pdf'];

  // Filter variables
  filterIsDefault: string | null = null;
  filterUploadType: string | null = null;
  filterDownloadType: string | null = null;
  filterId: string | null = null;

  // Debounce for filterId
  private filterIdChanged: Subject<string | null> = new Subject<string | null>();
  private filterIdSub?: Subscription;

  constructor(
    private packageService: PackageService,
    private modalService: NgbModal,
    private fb: FormBuilder,
    private toastr: ToastrService
  ) {
    this.packageForm = this.fb.group({
      id: ['', Validators.required],
      upload_type: [[], Validators.required],
      download_type: [[], Validators.required],
      monthly_price: [0, [Validators.required, Validators.min(0)]],
      yearly_price: [0, [Validators.required, Validators.min(0)]],
      is_default: [false],
      max_file_size_per_upload: [0, Validators.required],
      max_num_doc_per_upload: [0, Validators.required],
      limit_request: [0, Validators.required],
      enable_chatbot: [false],
      enable_term_compare: [false],
      enable_document_writting: [false],
      enable_document_summarize: [false],
      enable_document_compare: [false],
      total_llm_token: [0, Validators.required],
      total_document_page: [0, Validators.required],
      description: [''],
    });

    this.filterIdSub = this.filterIdChanged
      .pipe(debounceTime(400))
      .subscribe((val) => {
        this.filterId = val;
        this.page = 1;
        this.getPackages();
      });
  }

  onDetailToggle(row) {
    this.tablePackage.rowDetail.toggleExpandRow(row);
  }

  ngOnInit(): void {
    this.breadcrumbDefault = {
      links: [
        { 
          name: "Quản lý gói bán", 
          isHeader: true
        }
      ],
    };
    this.getPackages();
  }

  ngOnDestroy(): void {
    if (this.filterIdSub) {
      this.filterIdSub.unsubscribe();
    }
  }

  /**
   * Use debounce for filterId,
   * other filters trigger immediately as before.
   */
  applyFilter(params: {
    is_default?: string | null,
    upload_type?: string | null,
    download_type?: string | null,
    id?: string | null
  }) {
    if (params.is_default !== undefined) this.filterIsDefault = params.is_default;
    if (params.upload_type !== undefined) this.filterUploadType = params.upload_type;
    if (params.download_type !== undefined) this.filterDownloadType = params.download_type;

    if (params.id !== undefined) {
      this.filterIdChanged.next(params.id);
    } else {
      this.page = 1;
      this.getPackages();
    }
  }

  getPackages() {
    this.loading = true;
    const params: any = {
      page: this.page
    };

    if (this.filterIsDefault !== null && this.filterIsDefault !== undefined && this.filterIsDefault !== '') {
      params.is_default = this.filterIsDefault;
    }
    if (this.filterUploadType !== null && this.filterUploadType !== undefined && this.filterUploadType !== '') {
      params.upload_type = this.filterUploadType;
    }
    if (this.filterDownloadType !== null && this.filterDownloadType !== undefined && this.filterDownloadType !== '') {
      params.download_type = this.filterDownloadType;
    }
    if (this.filterId !== null && this.filterId !== undefined && this.filterId !== '') {
      params.id = this.filterId;
    }

    this.packageService.listPackages(params).subscribe({
      next: (res) => {
        this.packages = (res.results || []).map(pkg => ({
          ...pkg,
          upload_type: pkg.upload_type ? pkg.upload_type.split(',') : [],
          download_type: pkg.download_type ? pkg.download_type.split(',') : []
        }));
        this.total = res.count || 0;
        this.loading = false;
      },
      error: () => {
        this.loading = false;
        this.toastr.error('Lỗi khi lấy danh sách gói!', 'Lỗi');
      }
    });
  }

  pageChange(page: number) {
    this.page = page;
    this.getPackages();
  }

  openModal(content: any, pkg: any = null) {
    this.editingPackage = pkg;
    if (pkg) {
      this.packageForm.patchValue({
        ...pkg,
        upload_type: typeof pkg.upload_type === 'string' ? (pkg.upload_type ? pkg.upload_type.split(',') : []) : (pkg.upload_type || []),
        download_type: typeof pkg.download_type === 'string' ? (pkg.download_type ? pkg.download_type.split(',') : []) : (pkg.download_type || []),
      });
    } else {
      this.packageForm.reset({
        id: '',
        upload_type: [],
        download_type: [],
        monthly_price: null,
        yearly_price: null,
        is_default: false,
        max_file_size_per_upload: null,
        max_num_doc_per_upload: null,
        limit_request: null,
        enable_chatbot: false,
        enable_term_compare: false,
        enable_document_writting: false,
        enable_document_summarize: false,
        enable_document_compare: false,
        total_llm_token: null,
        total_document_page: null,
        description: '',
      });
    }
    this.modalRef = this.modalService.open(content, { size: 'lg', backdrop: 'static' });
  }

  submitPackage() {
    if (this.packageForm.invalid) return;

    const formValue = this.packageForm.value;
    const formData = new FormData();

    const uploadType =
      Array.isArray(formValue.upload_type) ? formValue.upload_type.join(',') : formValue.upload_type;
    const downloadType =
      Array.isArray(formValue.download_type) ? formValue.download_type.join(',') : formValue.download_type;

    formData.append('id', formValue.id ?? '');
    formData.append('upload_type', uploadType ?? '');
    formData.append('download_type', downloadType ?? '');
    formData.append('monthly_price', formValue.monthly_price != null ? formValue.monthly_price.toString() : '');
    formData.append('yearly_price', formValue.yearly_price != null ? formValue.yearly_price.toString() : '');
    formData.append('is_default', formValue.is_default ? 'true' : 'false');
    formData.append('max_file_size_per_upload', formValue.max_file_size_per_upload != null ? formValue.max_file_size_per_upload.toString() : '');
    formData.append('max_num_doc_per_upload', formValue.max_num_doc_per_upload != null ? formValue.max_num_doc_per_upload.toString() : '');
    formData.append('limit_request', formValue.limit_request != null ? formValue.limit_request.toString() : '');
    formData.append('enable_chatbot', formValue.enable_chatbot ? 'true' : 'false');
    formData.append('enable_term_compare', formValue.enable_term_compare ? 'true' : 'false');
    formData.append('enable_document_writting', formValue.enable_document_writting ? 'true' : 'false');
    formData.append('enable_document_summarize', formValue.enable_document_summarize ? 'true' : 'false');
    formData.append('enable_document_compare', formValue.enable_document_compare ? 'true' : 'false');
    formData.append('total_llm_token', formValue.total_llm_token != null ? formValue.total_llm_token.toString() : '');
    formData.append('total_document_page', formValue.total_document_page != null ? formValue.total_document_page.toString() : '');
    formData.append('description', formValue.description ?? '');

    if (this.editingPackage) {
      this.packageService.updatePackage(formValue.id, formData).subscribe({
        next: () => {
          this.getPackages();
          this.modalRef.close();
          this.toastr.success('Cập nhật gói thành công!', 'Thành công');
        },
        error: () => {
          this.toastr.error('Lỗi khi cập nhật gói!', 'Lỗi');
        }
      });
    } else {
      this.packageService.createPackage(formData).subscribe({
        next: () => {
          this.getPackages();
          this.modalRef.close();
          this.toastr.success('Tạo gói mới thành công!', 'Thành công');
        },
        error: () => {
          this.toastr.error('ID gói đã tồn tại!', 'Lỗi');
        }
      });
    }
  }

  // deletePackage(pkg: any) {
  //   Swal.fire({
  //     title: 'Bạn có chắc chắn muốn xoá package này?',
  //     text: '',
  //     icon: 'warning',
  //     showCancelButton: true,
  //     confirmButtonColor: '#3085d6',
  //     cancelButtonColor: '#d33',
  //     confirmButtonText: 'Xoá',
  //     cancelButtonText: 'Huỷ'
  //   }).then((result) => {
  //     if (result.isConfirmed) {
  //       this.packageService.deletePackage(pkg.id).subscribe({
  //         next: () => {
  //           this.getPackages();
  //           this.toastr.success('Xoá gói thành công!', 'Thành công');
  //         },
  //         error: () => {
  //           this.toastr.error('Lỗi khi xoá gói!', 'Lỗi');
  //         }
  //       });
  //     }
  //   });
  // }
}