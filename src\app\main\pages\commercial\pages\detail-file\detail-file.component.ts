import { Component, ChangeDetector<PERSON><PERSON>, OnDestroy, OnInit, OnChanges, SimpleChanges, ViewChildren, ViewChild, ElementRef, QueryList, HostListener, AfterViewInit, ViewEncapsulation, Input } from "@angular/core";
import { environment } from "environments/environment";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from "@angular/platform-browser";
import { marked } from "marked";
import { Subject } from "rxjs";
import { takeUntil, tap, filter, finalize, switchMap } from "rxjs/operators";

import { ToastrService } from "ngx-toastr";

import { AuthenticationService } from "app/auth/service";
import { ChatbotService } from "app/main/apps/quan-ly-van-ban/detail-work-space/chatbot/chatbot.service";
import { CommercialService } from "../../commercial.service";
import { CommercialChatService } from "../commercial-chat/commerial-chat.service";
import { randomUuidv4 } from "../../../../../../../util/randomUUID";

@Component({
  selector: "app-detail-file",
  templateUrl: "./detail-file.component.html",
  styleUrls: ["./detail-file.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class DetailFileComponent implements OnInit, OnChanges, OnDestroy, AfterViewInit {

  @Input() fileData: any = null;
  @Input() termId: string = null;
  private _destroy$ = new Subject<void>();
  showLoadingFile$ = this._clscChatService.showLoadingFile$;
  isSidebarCollapsed = this._clscService.isSidebarCollapsed$;

  constructor(
    private sanitizer: DomSanitizer,
    private authService: AuthenticationService,
    private _chatbotService: ChatbotService,
    private _toastService: ToastrService,
    private _clscService: CommercialService,
    private _clscChatService: CommercialChatService
  ) {

  }

  ngOnInit(): void {
    
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['fileData']) {
      setTimeout(() => {
        const element = document.getElementById(this.termId) as HTMLElement;
        if (element) {
          const container = element.closest(".clsc-file__body") as HTMLElement;
          if (container) {
            container.scrollTo({
              top: element.offsetTop - container.offsetTop - 10,
              behavior: "smooth"
            });

            element.classList.add("highlight-scroll");
            setTimeout(() => element.classList.remove("highlight-scroll"), 2500);
          }
        }
      }, 10);
    }
  }

  closeShowFile() { 
    this._clscChatService.hideFileRef();
    this._clscService.setSidebarCollapsed(false);
  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }
}
