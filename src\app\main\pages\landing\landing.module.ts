import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { MarkdownModule } from 'ngx-markdown';
import { CoreCommonModule } from "@core/common.module";
import { LandingWorkspaceComponent } from './landing-workspace/landing-workspace.component';
import { LandingWorkspaceFeatureComponent } from "./landing-workspace-feature/landing-workspace-feature.component";
import { LandingLegalDataComponent } from "./landing-legal-data/landing-legal-data.component";
import { LandingComponent } from "./landing.component";
import { LandingWorkspaceCardComponent } from "./landing-workspace-card/landing-workspace-card.component";
import { LandingArticleComponent } from "./landing-articles-carousel/landing-article/landing-article.component";
import { LandingArticlesCarouselComponent } from "./landing-articles-carousel/landing-articles-carousel.component";
import { LandingChatbotComponent } from "./landing-chatbot/landing-chatbot.component";
import { ArticleDetailComponent } from "./article-detail/article-detail.component";
import { LandingHeaderComponent } from './landing-header/landing-header.component';
import { LandingFooterComponent } from './landing-footer/landing-footer.component';

// routing
const routes: Routes = [
  {
    path: "landing",
    component: LandingComponent,
  },
  { path: "articles/:slug", component: ArticleDetailComponent }, 
];

@NgModule({
  declarations: [
    LandingComponent, 
    LandingWorkspaceCardComponent, 
    LandingArticleComponent,
    LandingArticlesCarouselComponent,
    LandingChatbotComponent,
    ArticleDetailComponent,
    LandingHeaderComponent,
    LandingFooterComponent,
    LandingWorkspaceComponent,
    LandingWorkspaceFeatureComponent,
    LandingLegalDataComponent,
  ],
  imports: [CommonModule, RouterModule.forChild(routes), CoreCommonModule, NgbModule, FormsModule,
    ReactiveFormsModule, HttpClientModule, MarkdownModule,],
})
export class LandingModule {}
