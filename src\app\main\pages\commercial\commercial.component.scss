.commercial-layout {
  display: flex;
  height: 100vh; // cho full màn hình
  background-color: #f3f4f6;
font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
}

.commercial-sidebar {
  position: relative;
  width: 260px;
  background: #111827;
  color: #e5e7eb;
  display: flex;
  flex-direction: column;
  padding: 16px 12px 0;
  border-right: 1px solid #0f172a;
  transition: width 0.2s ease;
}

.commercial-sidebar--collapsed {
  width: 72px;
}
.commercial-sidebar__toggle {
  position: absolute;
  right: -12px;                  // ra ngoài mép sidebar
  transform: none;
  width: 24px;
  height: 24px;
  border-radius: 999px;
  border: 0;
  background: #ffffff;
  box-shadow: 0 6px 16px rgba(15, 23, 42, 0.25);

  display: flex;
  align-items: center;
  justify-content: center;

  cursor: pointer;
  padding: 0;
  z-index: 10;
}

.commercial-sidebar__toggle span {
  font-size: 18px;
  line-height: 1;
  color: #4b5563;
}

.commercial-sidebar__toggle:hover {
  background: #f9fafb;
}


.commercial-sidebar__brand {
  display: flex;
  flex-direction: row; 
  align-items: center;
  gap: 8px;
  padding: 4px 4px 12px; 
  margin-bottom: 8px;
  border-bottom: 1px solid #111827;
}

// .commercial-sidebar__logo {
//   width: 40px;
//   height: 40px;
//   border-radius: 12px;
//   background: linear-gradient(135deg, #2563eb, #4f46e5);
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   font-weight: 700;
//   font-size: 18px;
// }

.commercial-sidebar__logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.commercial-sidebar__logo-img {
  height: 14px;
  display: block;
}

.commercial-sidebar__title {
  text-align: left;
}

.commercial-sidebar__title-main {
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0.03em;
  color: #f9fafb;
}

.commercial-sidebar__title-sub {
  margin-top: 2px;
  font-weight: 700;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.16em;
  color: #6b7280;
}

.commercial-sidebar__menu {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.commercial-sidebar__item {
  display: flex;
  align-items: center;
  // gap: 8px;
  width: 100%;
  padding: 12px 14px;
  border-radius: 8px;
  font-weight: 800;
  border: none;
  background: transparent;
  color: #9ca3af;
  font-size: 14px;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
}

.commercial-sidebar__item--active {
  background: #1d4ed8;
  transform: translateX(2px); 
  color: #ffffff;
  font-weight: 800;
}

.commercial-sidebar__item:not(.commercial-sidebar__item--active):hover {
  background: #111827;
  color: #e5e7eb;
}

.commercial-sidebar__user {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: auto; 
  padding: 12px 14px;
  border-top: 1px solid #111827;
}

.commercial-sidebar__avatar {
  width: 36px;
  height: 36px;
  border-radius: 999px;
  background: linear-gradient(135deg, #6366f1, #ec4899);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
}

/* MAIN */
.commercial-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.commercial-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #ffffff;
}

.commercial-header__title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #111827;
}

.commercial-header__subtitle {
  margin: 4px 0 0;
  font-size: 12px;
  color: #6b7280;
}

.commercial-header__link {
  font-size: 14px;
  color: #2563eb;
  text-decoration: none;
}

.commercial-header__link:hover {
  text-decoration: underline;
}

.commercial-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
  position: relative;
}
.commercial-tab,
.commercial-tab--full {
  animation: tabFadeIn 0.25s ease-out;
    height: 100%;   

}
@keyframes tabFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.commercial-section-title {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #111827;
}

.commercial-tab__desc {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 16px;
}

/* DASHBOARD */

.stat-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px rgba(15, 23, 42, 0.04);
}

.stat-card__label {
  font-size: 12px;
  color: #6b7280;
}

.stat-card__value {
  font-size: 22px;
  font-weight: 700;
  color: #111827;
  margin-top: 4px;
}

.stat-card__change {
  margin-top: 4px;
  font-size: 12px;
  font-weight: 600;
}

.stat-card__change--up {
  color: #16a34a;
}

.stat-card__change--down {
  color: #dc2626;
}

.commercial-dashboard__hint {
  font-size: 14px;
  color: #6b7280;
}

/* CHAT DEMO */

.chat-demo {
  max-width: 640px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chat-demo__bubble {
  padding: 10px 14px;
  font-size: 14px;
  border-radius: 16px;
  max-width: 80%;
}

.chat-demo__bubble--assistant {
  align-self: flex-start;
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.chat-demo__bubble--user {
  align-self: flex-end;
  background: #dbeafe;
  border: 1px solid #bfdbfe;
}

/* REPO TABLE */

.repo-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
}

.repo-table th,
.repo-table td {
  padding: 8px 10px;
  border-bottom: 1px solid #e5e7eb;
}

.repo-table th {
  background: #f9fafb;
  text-align: left;
  font-size: 12px;
  color: #6b7280;
}

/* COMPARE */

.compare-placeholder {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.compare-placeholder__box {
  background: #f9fafb;
  border-radius: 12px;
  padding: 12px;
  font-size: 14px;
  border: 1px solid #e5e7eb;
}

.compare-placeholder__box--new {
  background: #ecfdf5;
  border-color: #bbf7d0;
}

/* DRAFT */

.draft-textarea {
  width: 100%;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 12px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

/* REVIEW */

.review-card {
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  max-width: 680px;
}

.review-card__header {
  padding: 12px 16px;
  background: #eff6ff;
  font-size: 14px;
  font-weight: 600;
}

.review-card__body {
  padding: 12px 16px;
  font-size: 14px;
}

.review-card__body ul {
  padding-left: 18px;
}

/* PROMPT */

.prompt-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.prompt-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 10px 12px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.prompt-item__dot {
  width: 8px;
  height: 8px;
  border-radius: 999px;
  background: #22c55e;
  margin-top: 4px;
}

.prompt-item__title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.prompt-item__desc {
  font-size: 12px;
  color: #6b7280;
}
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

:host {
  font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    sans-serif;
}

/* 2. Layout chat */

.commercial-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.commercial-chat__body {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background: #f9fafb;
}

/* bubble demo */
.chat-demo__bubble {
  max-width: 640px;
  padding: 0.875rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.chat-demo__bubble--assistant {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  color: #111827;
}

.chat-demo__bubble--user {
  margin-left: auto;
  background: #e0f2fe;
  border: 1px solid #bae6fd;
  color: #0f172a;
}

/* footer + input bar */
.commercial-chat__footer {
  padding: 0.75rem 1.5rem 1.25rem;
  border-top: 1px solid #e5e7eb;
  background: #ffffff;
}

.chat-input {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  background: #f9fafb;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  padding: 0.5rem 0.75rem;
}

.chat-input__addon {
  border: none;
  background: transparent;
  width: 32px;
  height: 32px;
  border-radius: 0.5rem;
  font-size: 1.1rem;
  line-height: 1;
  color: #6b7280;
  cursor: pointer;
}

.chat-input__textarea {
  flex: 1;
  border: none;
  resize: none;
  background: transparent;
  font-size: 0.875rem;
  line-height: 1.4;
  max-height: 6rem;
  padding: 0.25rem 0;
  outline: none;
}

.chat-input__send {
  border: none;
  border-radius: 0.5rem;
  width: 36px;
  height: 36px;
  background: #2563eb;
  color: #ffffff;
  font-size: 1.25rem;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* 3. search bar */

.commercial-search {
  padding: 1.5rem 2rem 2rem;
}

.search-bar {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.search-bar__input {
  flex: 1;
  border-radius: 999px;
  border: 1px solid #e5e7eb;
  padding: 0.75rem 1.25rem;
  font-size: 0.875rem;
  outline: none;
}

.search-bar__input::placeholder {
  color: #9ca3af;
}

.search-bar__btn {
  border-radius: 999px;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  background: #2563eb;
  color: #ffffff;
  cursor: pointer;
}

.commercial-search__hint {
  margin-top: 1.25rem;
  font-size: 0.875rem;
  color: #6b7280;
}


.commercial-sidebar__user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.commercial-sidebar__user-name {
  font-size: 14px;
  font-weight: 600;
  color: #f9fafb;
}

.commercial-sidebar__user-plan {
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  color: #9ca3af;
}

/* Bánh răng */
.commercial-sidebar__user-menu {
  position: relative;
}

.commercial-sidebar__settings-btn {
  width: 28px;
  height: 28px;
  border-radius: 999px;
  border: 0;
  background: #111827;
  color: #e5e7eb;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(15, 23, 42, 0.4);
  transition: background-color 0.15s ease, transform 0.15s ease,
    box-shadow 0.15s ease;
}

.commercial-sidebar__settings-btn:hover {
  background: #1f2937;
  transform: translateY(-1px);
  box-shadow: 0 8px 18px rgba(15, 23, 42, 0.6);
}
.commercial-sidebar__settings-btn.dropdown-toggle::after,
.commercial-sidebar__settings-btn::after {
  display: none !important;
  content: none;
}

.commercial-sidebar__item-icon {
  width: 18px;
  height: 18px;
  display: inline-block;
  flex-shrink: 0;
  color: #9ca3af;
  font-size: 14px;
}

.commercial-sidebar__item--active .commercial-sidebar__item-icon {
  color: #2563eb;
}

.commercial-sidebar .top-24 {
  top: 24px;
}

.commercial-sidebar .top-34 {
  top: 34px;
}

