
import { Injectable } from "@angular/core";
import { ApiService } from "../../../../../../util/ServiceApi";
import { HttpClient } from "@angular/common/http";
import { environment } from "environments/environment";
import { BehaviorSubject } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class PackageService extends ApiService {
  public data: BehaviorSubject<any> = new BehaviorSubject(null);
  constructor(private http: HttpClient) {
    super(http, "package");
  }

  createPackage(body: any) {
    return this.http.post<any>(
      `${environment.apiUrl}/package/management/`,
      body
    );
  }

  listPackages(params: any) {
    return this.http.get<any>(`${environment.apiUrl}/package/management/`, {
      params,
    });
  }

  deletePackage(idPackage: any) {
    return this.http.delete<any>(
      `${environment.apiUrl}/package/management/${idPackage}`
    );
  }

  updatePackage(idPackage: any, body: any) {
    return this.http.put<any>(
      `${environment.apiUrl}/package/management/${idPackage}/`,
      body
    );
  }

  checkIdExists(params: any) {
    return this.http.get<any>(
      `${environment.apiUrl}/package/management/check-id-exists/`,
      { params }
    );
  }
}

